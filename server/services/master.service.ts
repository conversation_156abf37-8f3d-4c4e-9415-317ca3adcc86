import { prisma } from "../db";
import { errorResponse, successResponse } from "../utils/response.util";
import { CategoryInput, ItemTypeInput } from "../schemas/product.schema";

class MasterService {
  async getCategories() {
    try {
      const categories = await prisma.category.findMany({
        where: { isActive: true },
        orderBy: { name: 'asc' },
        include: {
          _count: {
            select: { 
              products: true,
              itemTypes: true
            }
          }
        }
      });

      return successResponse("Categories retrieved successfully", categories);
    } catch (error) {
      console.error("Get categories error:", error);
      return errorResponse("Failed to retrieve categories");
    }
  }

  async createCategory(data: CategoryInput) {
    try {
      // Check if category with same name already exists
      const existingCategory = await prisma.category.findUnique({
        where: { name: data.name }
      });

      if (existingCategory) {
        return errorResponse("Category with this name already exists");
      }

      const category = await prisma.category.create({
        data: {
          name: data.name,
          description: data.description
        }
      });

      return successResponse("Category created successfully", category);
    } catch (error) {
      console.error("Create category error:", error);
      return errorResponse("Failed to create category");
    }
  }

  async updateCategory(id: string, data: Partial<CategoryInput>) {
    try {
      // Check if category exists
      const existingCategory = await prisma.category.findUnique({
        where: { id }
      });

      if (!existingCategory) {
        return errorResponse("Category not found");
      }

      // Check if name is being updated and if it conflicts with another category
      if (data.name && data.name !== existingCategory.name) {
        const nameConflict = await prisma.category.findFirst({
          where: { 
            name: data.name,
            id: { not: id }
          }
        });

        if (nameConflict) {
          return errorResponse("Category with this name already exists");
        }
      }

      const category = await prisma.category.update({
        where: { id },
        data: {
          name: data.name,
          description: data.description
        }
      });

      return successResponse("Category updated successfully", category);
    } catch (error) {
      console.error("Update category error:", error);
      return errorResponse("Failed to update category");
    }
  }

  async deleteCategory(id: string) {
    try {
      // Check if category exists
      const existingCategory = await prisma.category.findUnique({
        where: { id },
        include: {
          _count: {
            select: { 
              products: true,
              itemTypes: true
            }
          }
        }
      });

      if (!existingCategory) {
        return errorResponse("Category not found");
      }

      // Check if category has associated products or item types
      if (existingCategory._count.products > 0) {
        return errorResponse("Cannot delete category with associated products");
      }

      if (existingCategory._count.itemTypes > 0) {
        return errorResponse("Cannot delete category with associated item types");
      }

      await prisma.category.delete({
        where: { id }
      });

      return successResponse("Category deleted successfully");
    } catch (error) {
      console.error("Delete category error:", error);
      return errorResponse("Failed to delete category");
    }
  }

  async getItemTypes(categoryId?: string) {
    try {
      const where: any = { isActive: true };
      if (categoryId) where.categoryId = categoryId;

      const itemTypes = await prisma.itemType.findMany({
        where,
        orderBy: { name: 'asc' },
        include: {
          category: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: { products: true }
          }
        }
      });

      return successResponse("Item types retrieved successfully", itemTypes);
    } catch (error) {
      console.error("Get item types error:", error);
      return errorResponse("Failed to retrieve item types");
    }
  }

  async createItemType(data: ItemTypeInput) {
    try {
      // Check if item type with same name already exists
      const existingItemType = await prisma.itemType.findUnique({
        where: { name: data.name }
      });

      if (existingItemType) {
        return errorResponse("Item type with this name already exists");
      }

      // If categoryId is provided, check if category exists
      if (data.categoryId) {
        const category = await prisma.category.findUnique({
          where: { id: data.categoryId }
        });

        if (!category) {
          return errorResponse("Category not found");
        }
      }

      const itemType = await prisma.itemType.create({
        data: {
          name: data.name,
          description: data.description,
          categoryId: data.categoryId
        },
        include: {
          category: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      return successResponse("Item type created successfully", itemType);
    } catch (error) {
      console.error("Create item type error:", error);
      return errorResponse("Failed to create item type");
    }
  }

  async updateItemType(id: string, data: Partial<ItemTypeInput>) {
    try {
      // Check if item type exists
      const existingItemType = await prisma.itemType.findUnique({
        where: { id }
      });

      if (!existingItemType) {
        return errorResponse("Item type not found");
      }

      // Check if name is being updated and if it conflicts with another item type
      if (data.name && data.name !== existingItemType.name) {
        const nameConflict = await prisma.itemType.findFirst({
          where: { 
            name: data.name,
            id: { not: id }
          }
        });

        if (nameConflict) {
          return errorResponse("Item type with this name already exists");
        }
      }

      // If categoryId is being updated, check if category exists
      if (data.categoryId) {
        const category = await prisma.category.findUnique({
          where: { id: data.categoryId }
        });

        if (!category) {
          return errorResponse("Category not found");
        }
      }

      const itemType = await prisma.itemType.update({
        where: { id },
        data: {
          name: data.name,
          description: data.description,
          categoryId: data.categoryId
        },
        include: {
          category: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      return successResponse("Item type updated successfully", itemType);
    } catch (error) {
      console.error("Update item type error:", error);
      return errorResponse("Failed to update item type");
    }
  }

  async deleteItemType(id: string) {
    try {
      // Check if item type exists
      const existingItemType = await prisma.itemType.findUnique({
        where: { id },
        include: {
          _count: {
            select: { products: true }
          }
        }
      });

      if (!existingItemType) {
        return errorResponse("Item type not found");
      }

      // Check if item type has associated products
      if (existingItemType._count.products > 0) {
        return errorResponse("Cannot delete item type with associated products");
      }

      await prisma.itemType.delete({
        where: { id }
      });

      return successResponse("Item type deleted successfully");
    } catch (error) {
      console.error("Delete item type error:", error);
      return errorResponse("Failed to delete item type");
    }
  }
}

export default new MasterService();
