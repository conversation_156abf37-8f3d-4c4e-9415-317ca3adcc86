
interface BuildUrlState {
    baseUrl: string;
    query: Record<string, any>;
}

export const buildUrl = ({ baseUrl, query }: BuildUrlState): string => {
    const queryString = new URLSearchParams(query).toString();

    return `${baseUrl}?${queryString}`;
};

export const formatUSD = (value: number) =>
    new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    }).format(value);

export const parseUSD = (value: string) =>
    parseFloat(value.replace(/[^0-9.]/g, '')) || 0;
