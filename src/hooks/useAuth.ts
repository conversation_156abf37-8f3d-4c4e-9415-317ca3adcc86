import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { authService, User } from '@/lib/auth-utils';

export interface UseAuthReturn {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  loginWithGoogle: () => Promise<void>;
  logout: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  error: string | null;
}

export function useAuth(): UseAuthReturn {
  const { data: session, status, update } = useSession();
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const user = session?.user ? {
    id: session.user.id,
    firstName: session.user.firstName,
    lastName: session.user.lastName,
    email: session.user.email || '',
    phoneNumber: session.user.phoneNumber,
  } : null;

  const isAuthenticated = status === 'authenticated' && !!session;

  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      setError(null);

      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        setError(result.error);
        return { success: false, error: result.error };
      }

      if (result?.ok) {
        router.push('/');
        return { success: true };
      }

      return { success: false, error: 'Login failed' };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithGoogle = async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);
      
      await signIn('google', { 
        callbackUrl: '/',
        redirect: true 
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Google login failed';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Clear local auth tokens
      await authService.logout();
      
      // Sign out from NextAuth
      await signOut({ 
        callbackUrl: '/auth/login',
        redirect: true 
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Logout failed';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshProfile = async (): Promise<void> => {
    try {
      setError(null);
      
      if (session?.accessToken) {
        const profile = await authService.getProfile();
        
        if (profile.status) {
          await update({
            user: {
              ...session.user,
              firstName: profile.data.firstName,
              lastName: profile.data.lastName,
              phoneNumber: profile.data.phoneNumber,
            }
          });
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh profile';
      setError(errorMessage);
    }
  };

  // Auto-refresh profile on mount if authenticated
  useEffect(() => {
    if (isAuthenticated && session?.accessToken) {
      refreshProfile();
    }
  }, [isAuthenticated]);

  // Clear error after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => setError(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  return {
    user,
    isLoading: isLoading || status === 'loading',
    isAuthenticated,
    login,
    loginWithGoogle,
    logout,
    refreshProfile,
    error,
  };
}
