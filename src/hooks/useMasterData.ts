'use client';

import { useQuery } from '@tanstack/react-query';
import { useAuthenticatedApi } from '@/services/useAuthQuery';
import { Category, ItemType } from '@/services/useProductQuery';

// Query keys
export const masterDataQueryKeys = {
  all: ['masterData'] as const,
  categories: () => [...masterDataQueryKeys.all, 'categories'] as const,
  itemTypes: () => [...masterDataQueryKeys.all, 'itemTypes'] as const,
  itemTypesByCategory: (categoryId: string) => [...masterDataQueryKeys.itemTypes(), categoryId] as const,
};

// Categories Query
export const useCategoriesQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: masterDataQueryKeys.categories(),
    queryFn: async (): Promise<Category[]> => {
      const response = await apiClient.get('/master/categories');
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

// Item Types Query
export const useItemTypesQuery = (categoryId?: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: categoryId ? masterDataQueryKeys.itemTypesByCategory(categoryId) : masterDataQueryKeys.itemTypes(),
    queryFn: async (): Promise<ItemType[]> => {
      const url = categoryId ? `/master/item-types?categoryId=${categoryId}` : '/master/item-types';
      const response = await apiClient.get(url);
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

// Combined hook for both categories and item types
export const useMasterData = () => {
  const categoriesQuery = useCategoriesQuery();
  const itemTypesQuery = useItemTypesQuery();

  return {
    categories: categoriesQuery.data || [],
    itemTypes: itemTypesQuery.data || [],
    isLoading: categoriesQuery.isLoading || itemTypesQuery.isLoading,
    isError: categoriesQuery.isError || itemTypesQuery.isError,
    error: categoriesQuery.error || itemTypesQuery.error,
    refetch: () => {
      categoriesQuery.refetch();
      itemTypesQuery.refetch();
    },
  };
};
