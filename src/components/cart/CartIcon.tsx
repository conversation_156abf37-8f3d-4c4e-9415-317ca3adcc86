"use client"
import React, { useState } from 'react'
import { Box, IconButton, Badge, useDisclosure } from '@chakra-ui/react'
import { FaShoppingCart } from 'react-icons/fa'
import { useCartQuery } from '@/services/useCartQuery'
import CartDrawer from './CartDrawer'
import { useRouter } from 'next/navigation'

const CartIcon: React.FC = () => {
    const { data: cart } = useCartQuery();
    const [isDrawerOpen, setIsDrawerOpen] = useState(false);
    const router = useRouter();

    const handleOpenCart = () => {
        setIsDrawerOpen(true);
    };

    const handleCloseCart = () => {
        setIsDrawerOpen(false);
    };

    const handleCheckout = () => {
        // Navigate to checkout page
        router.push('/checkout');
    };

    const itemCount = cart?.totalItems || 0;

    return (
        <>
            <Box position="relative">
                <IconButton
                    aria-label="Shopping cart"
                    icon={<FaShoppingCart />}
                    variant="ghost"
                    size="lg"
                    onClick={handleOpenCart}
                />
                {itemCount > 0 && (
                    <Badge
                        position="absolute"
                        top="-1"
                        right="-1"
                        colorScheme="red"
                        variant="solid"
                        borderRadius="full"
                        fontSize="xs"
                        minW="20px"
                        h="20px"
                        display="flex"
                        alignItems="center"
                        justifyContent="center"
                    >
                        {itemCount > 99 ? '99+' : itemCount}
                    </Badge>
                )}
            </Box>

            <CartDrawer
                isOpen={isDrawerOpen}
                onClose={handleCloseCart}
                onCheckout={handleCheckout}
            />
        </>
    );
};

export default CartIcon;
