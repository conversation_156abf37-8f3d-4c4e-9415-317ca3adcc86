"use client"
import React from 'react'
import {
    Box,
    Button,
    Text,
    VStack,
    HStack,
    Image,
    IconButton,
    Badge,
    Stack,
    Heading,
    Flex,
    Separator,
    <PERSON>er<PERSON><PERSON>,
    <PERSON>er<PERSON><PERSON>ger,
    <PERSON>er<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>er<PERSON><PERSON>er,
    <PERSON>er<PERSON><PERSON><PERSON>rigger,
    DrawerTitle
} from '@chakra-ui/react'
import { FaShoppingCart, FaTrash, FaMinus, FaPlus, FaTimes } from 'react-icons/fa'
import { formatUSD } from '@/utils/helpers/helper'
import { 
    useCartQuery, 
    useUpdateCartItemMutation, 
    useRemoveFromCartMutation,
    useClearCartMutation,
    CartItem 
} from '@/services/useCartQuery'

interface CartDrawerProps {
    isOpen: boolean;
    onClose: () => void;
    onCheckout?: () => void;
}

const CartItemComponent: React.FC<{ item: CartItem }> = ({ item }) => {
    const updateCartItemMutation = useUpdateCartItemMutation();
    const removeFromCartMutation = useRemoveFromCartMutation();

    const handleQuantityChange = async (newQuantity: number) => {
        if (newQuantity <= 0) {
            await removeFromCartMutation.mutateAsync(item.id);
        } else {
            await updateCartItemMutation.mutateAsync({
                itemId: item.id,
                data: { quantity: newQuantity }
            });
        }
    };

    const handleRemove = async () => {
        await removeFromCartMutation.mutateAsync(item.id);
    };

    const mainImage = item.product.images.find(img => img.isMain) || item.product.images[0];
    const itemTotal = item.price * item.quantity;

    return (
        <Box p={4} borderWidth="1px" borderRadius="md" bg="gray.50">
            <HStack gap={4} align="start">
                {/* Product Image */}
                <Box flexShrink={0}>
                    {mainImage ? (
                        <Image
                            src={mainImage.imageUrl}
                            alt={item.product.itemName}
                            boxSize="80px"
                            objectFit="cover"
                            borderRadius="md"
                        />
                    ) : (
                        <Box
                            boxSize="80px"
                            bg="gray.200"
                            borderRadius="md"
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                        >
                            <Text fontSize="xs" color="gray.500">No Image</Text>
                        </Box>
                    )}
                </Box>

                {/* Product Info */}
                <VStack align="start" flex={1} gap={2}>
                    <Text fontWeight="bold" fontSize="sm" noOfLines={2}>
                        {item.product.itemName}
                    </Text>
                    
                    <HStack justify="space-between" w="full">
                        <Text fontSize="sm" color="gray.600">
                            {formatUSD(item.price)} each
                        </Text>
                        <Text fontWeight="bold" color="green.600">
                            {formatUSD(itemTotal)}
                        </Text>
                    </HStack>

                    {/* Quantity Controls */}
                    <HStack justify="space-between" w="full">
                        <HStack>
                            <IconButton
                                aria-label="Decrease quantity"
                                icon={<FaMinus />}
                                size="xs"
                                variant="outline"
                                onClick={() => handleQuantityChange(item.quantity - 1)}
                                disabled={updateCartItemMutation.isPending}
                            />
                            <Text fontSize="sm" minW="30px" textAlign="center">
                                {item.quantity}
                            </Text>
                            <IconButton
                                aria-label="Increase quantity"
                                icon={<FaPlus />}
                                size="xs"
                                variant="outline"
                                onClick={() => handleQuantityChange(item.quantity + 1)}
                                disabled={updateCartItemMutation.isPending}
                            />
                        </HStack>

                        <IconButton
                            aria-label="Remove item"
                            icon={<FaTrash />}
                            size="xs"
                            colorScheme="red"
                            variant="ghost"
                            onClick={handleRemove}
                            disabled={removeFromCartMutation.isPending}
                        />
                    </HStack>
                </VStack>
            </HStack>
        </Box>
    );
};

const CartDrawer: React.FC<CartDrawerProps> = ({ isOpen, onClose, onCheckout }) => {
    const { data: cart, isLoading } = useCartQuery();
    const clearCartMutation = useClearCartMutation();

    const handleClearCart = async () => {
        await clearCartMutation.mutateAsync();
    };

    const handleCheckout = () => {
        if (onCheckout) {
            onCheckout();
        }
        onClose();
    };

    return (
        <DrawerRoot open={isOpen} onOpenChange={(details) => !details.open && onClose()}>
            <DrawerContent>
                <DrawerHeader>
                    <HStack justify="space-between">
                        <DrawerTitle>
                            <HStack>
                                <FaShoppingCart />
                                <Text>Shopping Cart</Text>
                                {cart && cart.totalItems > 0 && (
                                    <Badge colorScheme="blue" variant="solid">
                                        {cart.totalItems}
                                    </Badge>
                                )}
                            </HStack>
                        </DrawerTitle>
                        <DrawerCloseTrigger asChild>
                            <IconButton
                                aria-label="Close cart"
                                icon={<FaTimes />}
                                variant="ghost"
                                size="sm"
                            />
                        </DrawerCloseTrigger>
                    </HStack>
                </DrawerHeader>

                <DrawerBody>
                    {isLoading ? (
                        <VStack gap={4}>
                            <Text>Loading cart...</Text>
                        </VStack>
                    ) : !cart || cart.items.length === 0 ? (
                        <VStack gap={4} py={8} textAlign="center">
                            <FaShoppingCart size={48} color="gray" />
                            <Text fontSize="lg" color="gray.600">
                                Your cart is empty
                            </Text>
                            <Text fontSize="sm" color="gray.500">
                                Add some products to get started
                            </Text>
                        </VStack>
                    ) : (
                        <VStack gap={4} align="stretch">
                            {/* Cart Items */}
                            <VStack gap={3} align="stretch">
                                {cart.items.map((item) => (
                                    <CartItemComponent key={item.id} item={item} />
                                ))}
                            </VStack>

                            {/* Clear Cart Button */}
                            {cart.items.length > 0 && (
                                <Button
                                    variant="ghost"
                                    colorScheme="red"
                                    size="sm"
                                    onClick={handleClearCart}
                                    loading={clearCartMutation.isPending}
                                    disabled={clearCartMutation.isPending}
                                >
                                    Clear Cart
                                </Button>
                            )}
                        </VStack>
                    )}
                </DrawerBody>

                {cart && cart.items.length > 0 && (
                    <DrawerFooter>
                        <VStack gap={4} w="full">
                            <Separator />
                            
                            {/* Cart Summary */}
                            <HStack justify="space-between" w="full">
                                <Text fontSize="lg" fontWeight="bold">
                                    Total ({cart.totalItems} items):
                                </Text>
                                <Text fontSize="xl" fontWeight="bold" color="green.600">
                                    {formatUSD(cart.totalPrice)}
                                </Text>
                            </HStack>

                            {/* Checkout Button */}
                            <Button
                                colorScheme="blue"
                                size="lg"
                                w="full"
                                onClick={handleCheckout}
                            >
                                Proceed to Checkout
                            </Button>
                        </VStack>
                    </DrawerFooter>
                )}
            </DrawerContent>
        </DrawerRoot>
    );
};

export default CartDrawer;
