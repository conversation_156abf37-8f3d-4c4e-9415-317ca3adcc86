"use client"

import { <PERSON>, <PERSON><PERSON>tack, VStack, Text, Heading } from "@chakra-ui/react"
import { LuInfo, LuCheckCircle, LuAlertTriangle, LuXCircle } from "react-icons/lu"
import * as React from "react"

interface AlertProps {
  status?: "info" | "success" | "warning" | "error"
  children: React.ReactNode
  [key: string]: any
}

interface AlertIconProps {
  status?: "info" | "success" | "warning" | "error"
}

interface AlertTitleProps {
  children: React.ReactNode
}

interface AlertDescriptionProps {
  children: React.ReactNode
}

const statusConfig = {
  info: {
    icon: LuInfo,
    colorScheme: "blue",
    bg: "blue.50",
    borderColor: "blue.200",
    iconColor: "blue.500"
  },
  success: {
    icon: LuCheckCircle,
    colorScheme: "green",
    bg: "green.50",
    borderColor: "green.200",
    iconColor: "green.500"
  },
  warning: {
    icon: Lu<PERSON>lertTriangle,
    colorScheme: "orange",
    bg: "orange.50",
    borderColor: "orange.200",
    iconColor: "orange.500"
  },
  error: {
    icon: LuXCircle,
    colorScheme: "red",
    bg: "red.50",
    borderColor: "red.200",
    iconColor: "red.500"
  }
}

export function Alert({ status = "info", children, ...props }: AlertProps) {
  const config = statusConfig[status]
  
  return (
    <Box
      p={4}
      borderRadius="md"
      border="1px solid"
      borderColor={config.borderColor}
      bg={config.bg}
      {...props}
    >
      <HStack align="start" gap={3}>
        {children}
      </HStack>
    </Box>
  )
}

export function AlertIcon({ status = "info" }: AlertIconProps) {
  const config = statusConfig[status]
  const IconComponent = config.icon
  
  return (
    <Box color={config.iconColor} mt={0.5}>
      <IconComponent size={20} />
    </Box>
  )
}

export function AlertTitle({ children }: AlertTitleProps) {
  return (
    <Heading size="sm" mb={1}>
      {children}
    </Heading>
  )
}

export function AlertDescription({ children }: AlertDescriptionProps) {
  return (
    <Text fontSize="sm" color="gray.600">
      {children}
    </Text>
  )
}

// Export compound component
Alert.Icon = AlertIcon
Alert.Title = AlertTitle
Alert.Description = AlertDescription
