"use client"

import {
    Box,
    Button,
    Field,
    Flex,
    Icon,
    Image,
    Text,
    Stack,
    IconButton
} from '@chakra-ui/react'
import React, { useCallback, useState } from 'react'
import { FaCamera, FaTrash } from 'react-icons/fa'
import { useDropzone } from 'react-dropzone'

interface ImageFile {
    id: string
    file: File
    preview: string // base64 preview
    isMain?: boolean
}

interface FormImageUploadProps {
    label?: string
    description?: string
    required?: boolean
    errorText?: string
    maxFiles?: number
    maxSizeInMB?: number
    acceptedFileTypes?: string[]
    onImagesChange?: (images: ImageFile[]) => void
    value?: ImageFile[]
}

const FormImageUpload: React.FC<FormImageUploadProps> = ({
    label = "Product Images",
    description = "Upload multiple images of your product. First image will be the main image.",
    required = false,
    errorText,
    maxFiles = 10,
    maxSizeInMB = 5,
    acceptedFileTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    onImagesChange,
    value = []
}) => {
    const [images, setImages] = useState<ImageFile[]>(value)

    const onDrop = useCallback(async (acceptedFiles: File[]) => {
        if (images.length + acceptedFiles.length > maxFiles) {
            return;
        }

        // Process files to create base64 previews
        const processFiles = async () => {
            const newImages: ImageFile[] = await Promise.all(
                acceptedFiles.map(async (file, index) => {
                    // Convert file to base64 for preview
                    const base64 = await new Promise<string>((resolve) => {
                        const reader = new FileReader();
                        reader.onload = () => resolve(reader.result as string);
                        reader.readAsDataURL(file);
                    });

                    return {
                        id: `${Date.now()}-${index}`,
                        file,
                        preview: base64, // Use base64 instead of blob URL
                        isMain: images.length === 0 && index === 0
                    };
                })
            );

            const updatedImages = [...images, ...newImages].slice(0, maxFiles);
            setImages(updatedImages);
            onImagesChange?.(updatedImages);
        };

        await processFiles();
    }, [images, maxFiles, onImagesChange])

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        accept: acceptedFileTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
        maxSize: maxSizeInMB * 1024 * 1024,
        multiple: true
    })

    const removeImage = (id: string) => {
        const updatedImages = images.filter(img => img.id !== id)
        // If we removed the main image, make the first remaining image the main one
        if (updatedImages.length > 0 && !updatedImages.some(img => img.isMain)) {
            updatedImages[0].isMain = true
        }
        setImages(updatedImages)
        onImagesChange?.(updatedImages)
    }

    const setMainImage = (id: string) => {
        const updatedImages = images.map(img => ({
            ...img,
            isMain: img.id === id
        }))
        setImages(updatedImages)
        onImagesChange?.(updatedImages)
    }

    return (
        <Field.Root required={required}>
            <Field.Label fontWeight="bold" color="gray.600">
                {label}
                {required && <Field.RequiredIndicator>*</Field.RequiredIndicator>}
            </Field.Label>
            {description && (
                <Field.Label color="gray.500" fontSize="sm" mb={3}>
                    {description}
                </Field.Label>
            )}

            {/* Upload Area */}
            <Box
                {...getRootProps()}
                border="2px dashed"
                borderColor={isDragActive ? "blue.400" : "gray.300"}
                borderRadius="md"
                p={6}
                textAlign="center"
                cursor="pointer"
                bg={isDragActive ? "blue.50" : "gray.50"}
                transition="all 0.2s"
                _hover={{ borderColor: "blue.400", bg: "blue.50" }}
                mb={4}
            >
                <input {...getInputProps()} />
                <Stack gap={3} align="center">
                    <Icon as={FaCamera} boxSize={8} color="gray.400" />
                    <Text color="gray.600" fontWeight="medium">
                        {isDragActive ? "Drop images here..." : "Click or drag images here"}
                    </Text>
                    <Text fontSize="sm" color="gray.500">
                        Max {maxFiles} images, up to {maxSizeInMB}MB each
                    </Text>
                    <Text fontSize="xs" color="gray.400">
                        Supported: JPG, PNG, WebP
                    </Text>
                </Stack>
            </Box>

            {/* Image Preview Grid */}
            {images.length > 0 && (
                <Flex wrap="wrap" gap={3} mb={4}>
                    {images.map((image, index) => (
                        <Box
                            key={image.id}
                            position="relative"
                            borderRadius="md"
                            overflow="hidden"
                            w="120px"
                            h="120px"
                            border="1px solid"
                            borderColor="gray.200"
                        >
                            <Image
                                src={image.preview}
                                alt={`Product image ${index + 1}`}
                                w="100%"
                                h="100%"
                                objectFit="cover"
                                onError={(e) => {
                                    console.error('Image load error:', e);
                                }}
                            />

                            {/* Main Image Badge */}
                            {image.isMain && (
                                <Box
                                    position="absolute"
                                    top={1}
                                    left={1}
                                    bg="blue.500"
                                    color="white"
                                    px={2}
                                    py={1}
                                    borderRadius="sm"
                                    fontSize="xs"
                                    fontWeight="bold"
                                    zIndex={2}
                                >
                                    MAIN
                                </Box>
                            )}

                            {/* Remove Button */}
                            <IconButton
                                position="absolute"
                                top={1}
                                right={1}
                                aria-label="Remove image"
                                size="xs"
                                colorScheme="red"
                                onClick={() => removeImage(image.id)}
                                zIndex={2}
                            >
                                <FaTrash />
                            </IconButton>

                            {/* Set as Main Button */}
                            {!image.isMain && (
                                <Button
                                    position="absolute"
                                    bottom={1}
                                    left={1}
                                    right={1}
                                    size="xs"
                                    colorScheme="blue"
                                    variant="solid"
                                    onClick={() => setMainImage(image.id)}
                                    fontSize="xs"
                                    zIndex={2}
                                >
                                    Set as Main
                                </Button>
                            )}
                        </Box>
                    ))}
                </Flex>
            )}

            {errorText && <Field.ErrorText>{errorText}</Field.ErrorText>}
        </Field.Root>
    )
}

export default FormImageUpload
