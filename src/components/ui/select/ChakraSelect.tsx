import {
  Select,
  Portal,
  type SelectValueChangeDetails,
  ListCollection
} from "@chakra-ui/react";

export interface SelectItem {
  value: string;
  label: string;
  disabled?: boolean;
}

interface GroupedSelectProps {
  label?: string;
  placeholder?: string;
  width?: string;
  size?: "sm" | "md" | "lg";
  defaultValue?: string[];
  collection: ListCollection<SelectItem>;
  portalRef?: React.RefObject<HTMLDivElement | null>;
  onValueChange?: (details: SelectValueChangeDetails) => void;
}

export const ChakraSelect = ({
  label,
  placeholder = "-",
  width = "280px",
  size = "sm",
  defaultValue = [],
  collection,
  portalRef,
  onValueChange,
}: GroupedSelectProps) => {
  return (
    <Select.Root
      collection={collection}
      size={size}
      width={width}
      defaultValue={defaultValue}
      onValueChange={onValueChange} >
      <Select.HiddenSelect />
      {
        label && (
          <Select.Label>{label}</Select.Label>
        )
      }
      <Select.Control bg="white">
        <Select.Trigger>
          <Select.ValueText placeholder={placeholder} />
        </Select.Trigger>
        <Select.IndicatorGroup>
          <Select.Indicator />
        </Select.IndicatorGroup>
      </Select.Control>
      <Portal container={portalRef}>
        <Select.Positioner>
          <Select.Content>
            {collection.items.map((item) => (
              <Select.Item item={item} key={item.value}>
                {item.label}
                <Select.ItemIndicator />
              </Select.Item>
            ))}
          </Select.Content>
        </Select.Positioner>
      </Portal>
    </Select.Root>
  );
};
