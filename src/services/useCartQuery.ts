import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";
import { toaster } from "@/components/ui/toaster";

// Types
export interface CartItem {
  id: string;
  productId: string;
  quantity: number;
  price: number;
  product: {
    id: string;
    itemName: string;
    slug?: string;
    priceUSD: number;
    images: Array<{
      id: string;
      imageUrl: string;
      isMain: boolean;
    }>;
    sellType: 'auction' | 'buy-now';
    status: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface Cart {
  id: string;
  userId: string;
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  createdAt: string;
  updatedAt: string;
}

export interface AddToCartData {
  productId: string;
  quantity: number;
}

export interface UpdateCartItemData {
  quantity: number;
}

export interface WishlistItem {
  id: string;
  productId: string;
  product: {
    id: string;
    itemName: string;
    slug?: string;
    priceUSD: number;
    images: Array<{
      id: string;
      imageUrl: string;
      isMain: boolean;
    }>;
    sellType: 'auction' | 'buy-now';
    status: string;
  };
  createdAt: string;
}

export interface Wishlist {
  id: string;
  userId: string;
  items: WishlistItem[];
  totalItems: number;
  createdAt: string;
  updatedAt: string;
}

// Query Keys
export const cartQueryKeys = {
  all: ['cart'] as const,
  cart: () => [...cartQueryKeys.all, 'items'] as const,
  wishlist: () => [...cartQueryKeys.all, 'wishlist'] as const,
};

// Cart Queries
export const useCartQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: cartQueryKeys.cart(),
    queryFn: async (): Promise<Cart> => {
      const response = await apiClient.get('/cart');
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Add to Cart Mutation
export const useAddToCartMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: AddToCartData): Promise<CartItem> => {
      const response = await apiClient.post('/cart/items', data);
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate cart queries
      queryClient.invalidateQueries({ queryKey: cartQueryKeys.cart() });
      
      toaster.create({
        title: "Added to Cart",
        description: `${data.product.itemName} has been added to your cart.`,
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add item to cart",
        type: "error",
      });
    },
  });
};

// Update Cart Item Mutation
export const useUpdateCartItemMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ itemId, data }: { itemId: string; data: UpdateCartItemData }): Promise<CartItem> => {
      const response = await apiClient.put(`/cart/items/${itemId}`, data);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate cart queries
      queryClient.invalidateQueries({ queryKey: cartQueryKeys.cart() });
      
      toaster.create({
        title: "Cart Updated",
        description: "Cart item has been updated successfully.",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update cart item",
        type: "error",
      });
    },
  });
};

// Remove from Cart Mutation
export const useRemoveFromCartMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (itemId: string): Promise<void> => {
      await apiClient.delete(`/cart/items/${itemId}`);
    },
    onSuccess: () => {
      // Invalidate cart queries
      queryClient.invalidateQueries({ queryKey: cartQueryKeys.cart() });
      
      toaster.create({
        title: "Removed from Cart",
        description: "Item has been removed from your cart.",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to remove item from cart",
        type: "error",
      });
    },
  });
};

// Clear Cart Mutation
export const useClearCartMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (): Promise<void> => {
      await apiClient.delete('/cart');
    },
    onSuccess: () => {
      // Invalidate cart queries
      queryClient.invalidateQueries({ queryKey: cartQueryKeys.cart() });
      
      toaster.create({
        title: "Cart Cleared",
        description: "All items have been removed from your cart.",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to clear cart",
        type: "error",
      });
    },
  });
};

// Wishlist Queries
export const useWishlistQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: cartQueryKeys.wishlist(),
    queryFn: async (): Promise<Wishlist> => {
      const response = await apiClient.get('/wishlist');
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Add to Wishlist Mutation
export const useAddToWishlistMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (productId: string): Promise<WishlistItem> => {
      const response = await apiClient.post('/wishlist/items', { productId });
      return response.data;
    },
    onSuccess: (data) => {
      // Invalidate wishlist queries
      queryClient.invalidateQueries({ queryKey: cartQueryKeys.wishlist() });
      
      toaster.create({
        title: "Added to Wishlist",
        description: `${data.product.itemName} has been added to your wishlist.`,
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add item to wishlist",
        type: "error",
      });
    },
  });
};

// Remove from Wishlist Mutation
export const useRemoveFromWishlistMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (productId: string): Promise<void> => {
      await apiClient.delete(`/wishlist/items/${productId}`);
    },
    onSuccess: () => {
      // Invalidate wishlist queries
      queryClient.invalidateQueries({ queryKey: cartQueryKeys.wishlist() });
      
      toaster.create({
        title: "Removed from Wishlist",
        description: "Item has been removed from your wishlist.",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to remove item from wishlist",
        type: "error",
      });
    },
  });
};
