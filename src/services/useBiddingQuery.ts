import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useAuthenticatedApi } from "./useAuthQuery";
import { toaster } from "@/components/ui/toaster";

// Types
export interface Bid {
  id: string;
  productId: string;
  userId: string;
  bidAmount: number;
  bidTime: string;
  isWinning: boolean;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

export interface BidHistory {
  productId: string;
  bids: Bid[];
  totalBids: number;
  highestBid: number;
  currentWinner?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

export interface PlaceBidData {
  productId: string;
  bidAmount: number;
}

export interface UserBidSummary {
  productId: string;
  product: {
    id: string;
    itemName: string;
    slug?: string;
    images: Array<{
      id: string;
      imageUrl: string;
      isMain: boolean;
    }>;
    auctionEndDate: string;
    status: string;
  };
  highestBid: number;
  totalBids: number;
  isWinning: boolean;
  lastBidTime: string;
}

// Query Keys
export const biddingQueryKeys = {
  all: ['bidding'] as const,
  bidHistory: (productId: string) => [...biddingQueryKeys.all, 'history', productId] as const,
  userBids: () => [...biddingQueryKeys.all, 'user-bids'] as const,
  userBid: (productId: string) => [...biddingQueryKeys.all, 'user-bid', productId] as const,
};

// Get Bid History for a Product
export const useBidHistoryQuery = (productId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: biddingQueryKeys.bidHistory(productId),
    queryFn: async (): Promise<BidHistory> => {
      const response = await apiClient.get(`/products/${productId}/bids`);
      return response.data;
    },
    enabled: !!productId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 30 * 1000, // Refetch every 30 seconds for live updates
  });
};

// Get User's Bids Summary
export const useUserBidsQuery = () => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: biddingQueryKeys.userBids(),
    queryFn: async (): Promise<UserBidSummary[]> => {
      const response = await apiClient.get('/user/bids');
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get User's Highest Bid for a Product
export const useUserBidQuery = (productId: string) => {
  const apiClient = useAuthenticatedApi();

  return useQuery({
    queryKey: biddingQueryKeys.userBid(productId),
    queryFn: async (): Promise<Bid | null> => {
      const response = await apiClient.get(`/products/${productId}/user-bid`);
      return response.data;
    },
    enabled: !!productId,
    staleTime: 30 * 1000, // 30 seconds
  });
};

// Place Bid Mutation
export const usePlaceBidMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: PlaceBidData): Promise<Bid> => {
      const response = await apiClient.post(`/products/${data.productId}/bids`, {
        bidAmount: data.bidAmount
      });
      return response.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ 
        queryKey: biddingQueryKeys.bidHistory(variables.productId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: biddingQueryKeys.userBid(variables.productId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: biddingQueryKeys.userBids() 
      });
      
      // Also invalidate product queries to update current bid
      queryClient.invalidateQueries({ 
        queryKey: ['products', 'detail', variables.productId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['products', 'slug'] 
      });
      
      toaster.create({
        title: "Bid Placed Successfully",
        description: `Your bid of $${data.bidAmount} has been placed.`,
        type: "success",
      });
    },
    onError: (error, variables) => {
      const errorMessage = error instanceof Error ? error.message : "Failed to place bid";
      
      toaster.create({
        title: "Bid Failed",
        description: errorMessage,
        type: "error",
      });
      
      console.error('Failed to place bid:', error);
    },
  });
};

// Auto-bid Mutation (for future implementation)
export const useAutoBidMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { 
      productId: string; 
      maxBidAmount: number; 
      bidIncrement: number; 
    }): Promise<void> => {
      await apiClient.post(`/products/${data.productId}/auto-bid`, {
        maxBidAmount: data.maxBidAmount,
        bidIncrement: data.bidIncrement
      });
    },
    onSuccess: (_, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ 
        queryKey: biddingQueryKeys.bidHistory(variables.productId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: biddingQueryKeys.userBids() 
      });
      
      toaster.create({
        title: "Auto-bid Enabled",
        description: `Auto-bidding enabled with max bid of $${variables.maxBidAmount}.`,
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Auto-bid Failed",
        description: error instanceof Error ? error.message : "Failed to enable auto-bidding",
        type: "error",
      });
    },
  });
};

// Cancel Auto-bid Mutation
export const useCancelAutoBidMutation = () => {
  const apiClient = useAuthenticatedApi();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (productId: string): Promise<void> => {
      await apiClient.delete(`/products/${productId}/auto-bid`);
    },
    onSuccess: (_, productId) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ 
        queryKey: biddingQueryKeys.userBids() 
      });
      
      toaster.create({
        title: "Auto-bid Cancelled",
        description: "Auto-bidding has been disabled for this product.",
        type: "success",
      });
    },
    onError: (error) => {
      toaster.create({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to cancel auto-bidding",
        type: "error",
      });
    },
  });
};
