'use client'
import { Box, Heading, Text, VStack, Skeleton, Stack } from '@chakra-ui/react';
import { useParams, useRouter } from 'next/navigation';
import ProductImageGallery from '@/components/product/ProductImageGallery';
import ProductDetailLayout from '@/components/product/ProductDetailLayout';
import BidInfo from '@/components/product/BidInfo';
import AuctionInfo from '@/components/product/AuctionInfo';
import BuyNowInfo from '@/components/product/BuyNowInfo';
import SalesHistory from '@/components/product/SalesHistory';
import Breadcrumb from '@/components/ui/Breadcrumb';
import { useProductBySlugQuery } from '@/services/useProductQuery';
import { useAddToCartMutation } from '@/services/useCartQuery';
import { useBuyNowMutation } from '@/services/useCheckoutQuery';
import { formatDistanceToNow } from 'date-fns';
import ProductCategory from '@/components/product/ProductCategory';
import products from '@/datas/product.json';
import ProductInfo from '@/components/product/ProductInfo';
import { formatUSD } from '@/utils/helpers/helper';
import { toaster } from '@/components/ui/toaster';

const ProductDetailPage = () => {
    const { locale, category, slug } = useParams();
    const router = useRouter();

    const {
        data: product,
        isLoading,
        error,
        isError
    } = useProductBySlugQuery(slug as string);

    // Mutations for cart and buy now
    const addToCartMutation = useAddToCartMutation();
    const buyNowMutation = useBuyNowMutation();

    // Loading state
    if (isLoading) {
        return (
            <Box>
                <ProductDetailLayout
                    leftContent={
                        <VStack gap={4} align="stretch">
                            <Box px={{ base: 0, md: 6 }}>
                                <Skeleton height="20px" width="300px" />
                            </Box>
                            <Box px={{ base: 0, md: 6 }}>
                                <Skeleton height={{ base: '300px', lg: '500px', xl: '650px' }} />
                            </Box>
                        </VStack>
                    }
                    rightContent={
                        <Stack gap={6}>
                            <Skeleton height="40px" />
                            <Skeleton height="20px" />
                            <Skeleton height="100px" />
                            <Skeleton height="200px" />
                        </Stack>
                    }
                />
            </Box>
        );
    }

    // Error state
    if (isError || !product) {
        return (
            <Box textAlign="center" py={20}>
                <Text fontSize="xl" color="red.500" mb={4}>
                    {error instanceof Error ? error.message : 'Product not found'}
                </Text>
                <Text color="gray.600">
                    The product you're looking for doesn't exist or has been removed.
                </Text>
            </Box>
        );
    }

    // Breadcrumb items
    const breadcrumbItems = [
        {
            label: 'Home',
            href: '/'
        },
        {
            label: 'Auction',
            href: '/auction'
        },
        {
            label: product.itemName,
            isCurrentPage: true
        }
    ];

    // Calculate time left for auction
    const timeLeft = product.auctionEndDate
        ? formatDistanceToNow(new Date(product.auctionEndDate), { addSuffix: true })
        : null;

    // Handle add to cart for buy-now products
    const handleAddToCart = async (productId: string, quantity: number) => {
        try {
            await addToCartMutation.mutateAsync({
                productId,
                quantity
            });
        } catch (error) {
            console.error('Failed to add to cart:', error);
        }
    };

    // Handle buy now for buy-now products
    const handleBuyNow = async (productId: string, quantity: number) => {
        try {
            // For now, redirect to checkout with product info
            // In a real app, you'd collect shipping/payment info first
            router.push(`/checkout/buy-now?productId=${productId}&quantity=${quantity}`);
        } catch (error) {
            console.error('Failed to buy now:', error);
        }
    };

    // Handle bid placement for auction products
    const handlePlaceBid = () => {
        console.log('Place bid for product:', product.id);
        // This will be handled by AuctionInfo component
    };

    // Handle bid history
    const handleShowBidHistory = () => {
        console.log('Show bid history for product:', product.id);
        // This will be handled by AuctionInfo component
    };

    // Handle sales history
    const handleViewSalesHistory = () => {
        console.log('View sales history for product:', product.id);
        // TODO: Implement sales history view logic
    };

    // Transform API product to match ProductItem interface for existing components
    const transformedProduct = {
        id: product.id,
        slug: product.slug || '',
        title: product.itemName,
        image: product.images.find(img => img.isMain)?.imageUrl || product.images[0]?.imageUrl || '',
        images: product.images.map(img => img.imageUrl), // ProductItem expects string[] for images
        price: `$${product.priceUSD}`, // ProductItem expects string for price
        bids: product.bidCount?.toString() || '0', // ProductItem expects string for bids
        timeLeft: timeLeft || '',
    };

    console.log(transformedProduct)

    const leftContent = (
        <VStack gap={4} align="stretch">
            <Box px={{ base: 0, md: 6 }}>
                <Breadcrumb items={breadcrumbItems} />
            </Box>

            <Box
                position="sticky"
                top={{ base: 4, md: 8 }}
                px={{ base: 0, md: 6 }}
            >
                <ProductImageGallery
                    item={transformedProduct}
                    boxSizeWatchList={6}
                    containerProps={{
                        height: { base: 'full', lg: '500px', xl: '650px' },
                    }}
                />
            </Box>
        </VStack>
    );

    const rightContent = (
        <>
            <ProductInfo
                title={product.itemName}
                subtitle={product.category?.name || '-'}
                mb={6}
            />

            {/* Conditional rendering based on sell type */}
            {product.sellType === 'auction' ? (
                <AuctionInfo
                    currentBid={product.currentBid ? Number(product.currentBid) : Number(product.priceUSD)}
                    startingPrice={Number(product.priceUSD)}
                    bidCount={product.bidCount ?? 0}
                    auctionStartDate={product.auctionStartDate || new Date().toISOString()}
                    auctionEndDate={product.auctionEndDate || new Date().toISOString()}
                    extendedBiddingEnabled={product.extendedBiddingEnabled ?? false}
                    extendedBiddingMinutes={product.extendedBiddingMinutes ?? 5}
                    extendedBiddingDuration={product.extendedBiddingDuration ?? 10}
                    productId={product.id}
                    productName={product.itemName}
                    onPlaceBid={handlePlaceBid}
                    onShowBidHistory={handleShowBidHistory}
                    mb={6}
                />
            ) : (
                <BuyNowInfo
                    price={Number(product.priceUSD)}
                    stock={1} // Assuming stock is 1 for collectibles
                    productId={product.id}
                    productName={product.itemName}
                    onAddToCart={handleAddToCart}
                    onBuyNow={handleBuyNow}
                    mb={6}
                />
            )}

            <Box as="hr" my={6} borderColor="gray.200" />

            <Box>
                <Heading as="h3" size="md" mb={3}>
                    Description
                </Heading>
                <Text fontSize="sm" mb={4}>
                    {product.description || 'No description available for this product.'}
                </Text>
            </Box>

            <Box as="hr" my={6} borderColor="gray.200" />

            <SalesHistory
                onLinkClick={handleViewSalesHistory}
            />
        </>
    );

    return (
        <Box>
            <Box>
                <ProductDetailLayout
                    leftContent={leftContent}
                    rightContent={rightContent}
                />
            </Box>

            <ProductCategory
                items={products}
                title="Related Items"
            />

            <ProductCategory
                items={products}
                title="Similar Items"
            />
        </Box>
    );
};

export default ProductDetailPage;