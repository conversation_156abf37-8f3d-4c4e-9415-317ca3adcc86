'use client';

import ProductCard from '@/components/product/ProductCard';
import { ChakraSelect } from '@/components/ui/select/ChakraSelect';
import {
    Box,
    Grid,
    GridItem,
    Heading,
    VStack,
    HStack,
    Text,
    Input,
    InputGroup,
    Checkbox,
    CheckboxGroup,
    SimpleGrid,
    createListCollection,
    Tabs,
    Fieldset,
    For,
    Icon
} from '@chakra-ui/react';
import { useState } from 'react';
import products from '@/datas/product.json';
import { LuUser } from 'react-icons/lu';
import { FaTimes, FaTimesCircle } from 'react-icons/fa';
import { MenuList } from '@/components/layouts/front/navbar/Navbar';
import { notFound, useParams, usePathname } from 'next/navigation';

const AllProductsPage = () => {
    const pathname = usePathname();
    const params = useParams();
    const { locale } = params;
    const [priceRange, setPriceRange] = useState({ min: '', max: '' });
    const [yearRange, setYearRange] = useState({ min: '', max: '' });
    const [gradeRange, setGradeRange] = useState({ min: '', max: '' });
    const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
    const [selectedAuthenticators, setSelectedAuthenticators] = useState<string[]>([]);
    const [selectedEyeAppeal, setSelectedEyeAppeal] = useState<string[]>([]);
    const [sortOption, setSortOption] = useState('ending');

    const cleanedPath = pathname.replace(`/${locale}`, '') || '/';
    const menuExists = MenuList.some(menu => {
        const menuHref = menu.href.split('?')[0].split('#')[0];
        return menuHref === cleanedPath;
    });
    if (!menuExists) {
        if (typeof window !== "undefined") {
            return notFound();
        }
        return null;
    }

    return (
        <Box p={{ base: 4, md: 6 }} mx="auto">
            <Grid templateColumns={{ base: '1fr', md: '300px 1fr' }} gap={6}>
                <GridItem
                    zIndex={1}
                    bg="white"
                    p={4}
                    borderRadius={'md'}
                    display="inline-block"
                    height={'fit-content'}
                    position={'sticky'}
                    top="100px"
                >
                    <VStack align="stretch" gap={6} mb={6} display="flex">
                        <Text fontSize="xl" fontWeight="bold">
                            Filter
                        </Text>
                        <Tabs.Root defaultValue="members" variant="plain">
                            <Tabs.List bg="bg.emphasized" rounded="3xl" p="1" whiteSpace={"nowrap"}>
                                <Tabs.Trigger value="members">
                                    Auction
                                </Tabs.Trigger>
                                <Tabs.Trigger value="projects">
                                    Buy Now
                                </Tabs.Trigger>
                                <Tabs.Indicator rounded="3xl" />
                            </Tabs.List>
                        </Tabs.Root>
                        <Box>
                            <Heading size="sm" mb={2}>Year</Heading>
                            <HStack>
                                <Input
                                    placeholder="Min"
                                    value={yearRange.min}
                                    onChange={(e) => setYearRange({ ...yearRange, min: e.target.value })}
                                />
                                <Input
                                    placeholder="Max"
                                    value={yearRange.max}
                                    onChange={(e) => setYearRange({ ...yearRange, max: e.target.value })}
                                />
                            </HStack>
                        </Box>
                        <Box>
                            <Heading size="sm" mb={2}>Price</Heading>
                            <HStack>
                                <InputGroup startElement="$" endElement="USD">
                                    <Input
                                        placeholder="Min"
                                        value={priceRange.min}
                                        onChange={(e) => setPriceRange({ ...priceRange, min: e.target.value })}
                                    />
                                </InputGroup>
                                <InputGroup startElement="$" endElement="USD">
                                    <Input
                                        placeholder="Max"
                                        value={priceRange.max}
                                        onChange={(e) => setPriceRange({ ...priceRange, max: e.target.value })}
                                    />
                                </InputGroup>
                            </HStack>
                        </Box>
                        <Box>
                            <Heading size="sm" mb={2}>Grade</Heading>
                            <HStack>
                                <Input
                                    placeholder="From"
                                    value={gradeRange.min}
                                    onChange={(e) => setGradeRange({ ...gradeRange, min: e.target.value })}
                                />
                                <Input
                                    placeholder="To"
                                    value={gradeRange.max}
                                    onChange={(e) => setGradeRange({ ...gradeRange, max: e.target.value })}
                                />
                            </HStack>
                        </Box>
                        <Fieldset.Root>
                            <CheckboxGroup defaultValue={["react"]} name="framework">
                                <Fieldset.Legend fontSize="sm" mb="2">
                                    <Text fontSize="sm" fontWeight="semibold">Category</Text>
                                </Fieldset.Legend>
                                <Fieldset.Content>
                                    <For each={["Football", "Basketball", "Hockey", "Pokemon"]}>
                                        {(value) => (
                                            <Checkbox.Root key={value} value={value}>
                                                <Checkbox.HiddenInput />
                                                <Checkbox.Control />
                                                <Checkbox.Label>{value}</Checkbox.Label>
                                            </Checkbox.Root>
                                        )}
                                    </For>
                                </Fieldset.Content>
                            </CheckboxGroup>
                        </Fieldset.Root>
                        <Fieldset.Root>
                            <CheckboxGroup defaultValue={["react"]} name="framework">
                                <Fieldset.Legend fontSize="sm" mb="2">
                                    <Text fontSize="sm" fontWeight="semibold">Category</Text>
                                </Fieldset.Legend>
                                <Fieldset.Content>
                                    <For each={["Football", "Basketball", "Hockey", "Pokemon"]}>
                                        {(value) => (
                                            <Checkbox.Root key={value} value={value}>
                                                <Checkbox.HiddenInput />
                                                <Checkbox.Control />
                                                <Checkbox.Label>{value}</Checkbox.Label>
                                            </Checkbox.Root>
                                        )}
                                    </For>
                                </Fieldset.Content>
                            </CheckboxGroup>
                        </Fieldset.Root>
                    </VStack>
                </GridItem>

                <GridItem>
                    <Box mb={6}>
                        <HStack justify="space-between" alignItems="center">
                            <Box>
                                <Text fontSize="sm" fontWeight="bold">
                                    Showing 1 - 20 of 1000
                                </Text>
                            </Box>
                            <HStack>
                                <Text fontSize="sm" fontWeight="bold">
                                    Sort By :
                                </Text>
                                <ChakraSelect
                                    placeholder={`Pilih`}
                                    collection={createListCollection({
                                        items: [
                                            { value: "price-asc", label: "Price: Low to High" },
                                            { value: "price-desc", label: "Price: High to Low" },
                                            { value: "bids", label: "Most Bids" },
                                            { value: "ending", label: "Ending Soonest" }
                                        ],
                                        itemToValue: (item) => item.value,
                                    })}
                                    defaultValue={["price-asc"]}
                                    onValueChange={(e) => {

                                    }}
                                />
                            </HStack>
                        </HStack>
                        <HStack alignItems={'center'} gap={2}>
                            <Box
                                borderWidth={1}
                                borderColor="gray.300"
                                borderStyle="solid"
                                borderRadius="lg"
                                px={3}
                                py={2}
                                display="flex"
                                alignItems="center"
                                gap={4}
                                bg="white"
                            >
                                <Text fontSize="sm" color="gray.800">
                                    Harga Minimum
                                </Text>
                                <Box>
                                    <Icon
                                        as={FaTimesCircle}
                                        boxSize={4}
                                        color="gray.600"
                                        cursor="pointer"
                                    />
                                </Box>
                            </Box>
                            <Box
                                borderWidth={1}
                                borderColor="gray.300"
                                borderStyle="solid"
                                borderRadius="lg"
                                px={3}
                                py={2}
                                display="flex"
                                alignItems="center"
                                gap={4}
                                bg="white"
                            >
                                <Text fontSize="sm" color="gray.800">
                                    Category
                                </Text>
                                <Box>
                                    <Icon
                                        as={FaTimesCircle}
                                        boxSize={4}
                                        color="gray.600"
                                        cursor="pointer"
                                    />
                                </Box>
                            </Box>
                        </HStack>
                    </Box>

                    <Box>
                        <SimpleGrid alignItems={'start'} columns={{ base: 1, sm: 2, lg: 3, xl: 3 }} gapX={4} gapY={2}>
                            {products.map(item => (
                                <GridItem key={item.id} mb={4}>
                                    <ProductCard
                                        item={item}
                                        backgroundColorCardImage="none"
                                        containerProps={{
                                            maxW: 'full',
                                            maxH: 'full',
                                            backgroundColor: 'white',
                                            px: 4,
                                            pb: 6,
                                            borderRadius: 'md',
                                        }}
                                        imageContainerProps={{
                                            aspectRatio: '1/1',
                                        }}
                                        bodyContainerProps={{
                                            borderTopWidth: 1,
                                            borderTopColor: 'gray.200',
                                            borderTopStyle: 'solid',
                                            pt: 4,
                                        }}
                                        imageProps={{ objectFit: 'cover' }}
                                    />
                                </GridItem>
                            ))}
                        </SimpleGrid>
                    </Box>
                </GridItem>
            </Grid>
        </Box>
    );
};

export default AllProductsPage;