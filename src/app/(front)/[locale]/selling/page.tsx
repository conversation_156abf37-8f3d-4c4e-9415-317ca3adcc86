'use client'
import React, { useState } from 'react'
import {
    Box,
    <PERSON>ton,
    Card,
    Heading,
    Text,
    VStack,
    HStack,
    Stack,
    Image,
    Badge,
    Grid,
    GridItem,
    Select,
    Input,
    InputGroup,
    InputLeftElement,
    IconButton,
    Menu,
    MenuButton,
    MenuList,
    MenuItem,
    Alert,
    AlertIcon,
    AlertTitle,
    AlertDescription,
    Skeleton,
    Flex,
    Stat,
    StatLabel,
    StatNumber,
    StatHelpText,
} from '@chakra-ui/react'
import {
    FaSearch,
    FaPlus,
    FaEdit,
    FaTrash,
    FaEye,
    FaEllipsisV,
    FaGavel,
    FaShoppingCart,
    FaClock,
    FaDollarSign
} from 'react-icons/fa'
import { useProductsQuery, useDeleteProductMutation, ProductQueryParams } from '@/services/useProductQuery'
import { formatUSD } from '@/utils/helpers/helper'
import { formatDistanceToNow } from 'date-fns'
import { useRouter } from 'next/navigation'
import { toaster } from '@/components/ui/toaster'
import { useSession } from '@/hooks/useSession'

const SellingPage = () => {
    const router = useRouter()
    const { user } = useSession()
    const [searchTerm, setSearchTerm] = useState('')
    const [statusFilter, setStatusFilter] = useState<string>('all')
    const [sellTypeFilter, setSellTypeFilter] = useState<string>('all')

    // Query parameters for user's products
    const queryParams: ProductQueryParams = {
        sellerId: user?.id,
        search: searchTerm || undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        sellType: sellTypeFilter !== 'all' ? (sellTypeFilter as 'auction' | 'buy-now') : undefined,
        sortBy: 'createdAt',
        sortOrder: 'desc',
        limit: 20
    }

    const { data: productsData, isLoading, error } = useProductsQuery(queryParams)
    const deleteProductMutation = useDeleteProductMutation()

    const handleSearch = (value: string) => {
        setSearchTerm(value)
    }

    const handleDeleteProduct = async (productId: string, productName: string) => {
        if (window.confirm(`Are you sure you want to delete "${productName}"? This action cannot be undone.`)) {
            try {
                await deleteProductMutation.mutateAsync(productId)
            } catch (error) {
                console.error('Failed to delete product:', error)
            }
        }
    }

    const handleEditProduct = (productId: string) => {
        router.push(`/selling/edit/${productId}`)
    }

    const handleViewProduct = (slug: string) => {
        router.push(`/products/${slug}`)
    }

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active': return 'green'
            case 'draft': return 'gray'
            case 'sold': return 'blue'
            case 'cancelled': return 'red'
            default: return 'gray'
        }
    }

    const getStatusText = (status: string) => {
        switch (status) {
            case 'active': return 'Active'
            case 'draft': return 'Draft'
            case 'sold': return 'Sold'
            case 'cancelled': return 'Cancelled'
            default: return status
        }
    }

    if (!user) {
        return (
            <Box maxW="6xl" mx="auto" p={6} textAlign="center">
                <Alert status="warning">
                    <AlertIcon />
                    <AlertTitle>Please log in!</AlertTitle>
                    <AlertDescription>
                        You need to be logged in to view your selling items.
                    </AlertDescription>
                </Alert>
                <Button mt={4} onClick={() => router.push('/auth/login')}>
                    Log In
                </Button>
            </Box>
        )
    }

    const products = productsData?.products || []
    const activeProducts = products.filter(p => p.status === 'active')
    const soldProducts = products.filter(p => p.status === 'sold')
    const totalEarnings = soldProducts.reduce((sum, p) => sum + Number(p.priceUSD), 0)

    return (
        <Box maxW="6xl" mx="auto" p={6}>
            {/* Header */}
            <Flex justify="space-between" align="center" mb={6}>
                <VStack align="start" gap={1}>
                    <Heading size="xl">My Selling Items</Heading>
                    <Text color="gray.600">
                        Manage your collectibles and auctions
                    </Text>
                </VStack>
                <Button
                    colorScheme="blue"
                    leftIcon={<FaPlus />}
                    onClick={() => router.push('/selling/create')}
                >
                    List New Item
                </Button>
            </Flex>

            {/* Stats Cards */}
            <Grid templateColumns={{ base: "1fr", md: "repeat(4, 1fr)" }} gap={4} mb={6}>
                <Card.Root>
                    <Card.Body textAlign="center">
                        <Stat>
                            <StatLabel>Total Items</StatLabel>
                            <StatNumber color="blue.600">{products.length}</StatNumber>
                            <StatHelpText>All time</StatHelpText>
                        </Stat>
                    </Card.Body>
                </Card.Root>

                <Card.Root>
                    <Card.Body textAlign="center">
                        <Stat>
                            <StatLabel>Active Listings</StatLabel>
                            <StatNumber color="green.600">{activeProducts.length}</StatNumber>
                            <StatHelpText>Currently selling</StatHelpText>
                        </Stat>
                    </Card.Body>
                </Card.Root>

                <Card.Root>
                    <Card.Body textAlign="center">
                        <Stat>
                            <StatLabel>Items Sold</StatLabel>
                            <StatNumber color="purple.600">{soldProducts.length}</StatNumber>
                            <StatHelpText>Completed sales</StatHelpText>
                        </Stat>
                    </Card.Body>
                </Card.Root>

                <Card.Root>
                    <Card.Body textAlign="center">
                        <Stat>
                            <StatLabel>Total Earnings</StatLabel>
                            <StatNumber color="orange.600">{formatUSD(totalEarnings)}</StatNumber>
                            <StatHelpText>From sold items</StatHelpText>
                        </Stat>
                    </Card.Body>
                </Card.Root>
            </Grid>

            {/* Filters */}
            <Card.Root mb={6}>
                <Card.Body>
                    <Grid templateColumns={{ base: "1fr", md: "2fr 1fr 1fr" }} gap={4}>
                        <InputGroup>
                            <InputLeftElement>
                                <FaSearch color="gray" />
                            </InputLeftElement>
                            <Input
                                placeholder="Search your items..."
                                value={searchTerm}
                                onChange={(e) => handleSearch(e.target.value)}
                            />
                        </InputGroup>

                        <Select
                            value={statusFilter}
                            onChange={(e) => setStatusFilter(e.target.value)}
                        >
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="draft">Draft</option>
                            <option value="sold">Sold</option>
                            <option value="cancelled">Cancelled</option>
                        </Select>

                        <Select
                            value={sellTypeFilter}
                            onChange={(e) => setSellTypeFilter(e.target.value)}
                        >
                            <option value="all">All Types</option>
                            <option value="auction">Auction</option>
                            <option value="buy-now">Buy Now</option>
                        </Select>
                    </Grid>
                </Card.Body>
            </Card.Root>

            {/* Products List */}
            {isLoading ? (
                <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }} gap={6}>
                    {[...Array(6)].map((_, i) => (
                        <Card.Root key={i}>
                            <Card.Body>
                                <VStack gap={4}>
                                    <Skeleton height="200px" width="100%" />
                                    <Skeleton height="20px" width="80%" />
                                    <Skeleton height="16px" width="60%" />
                                </VStack>
                            </Card.Body>
                        </Card.Root>
                    ))}
                </Grid>
            ) : error ? (
                <Alert status="error">
                    <AlertIcon />
                    <AlertTitle>Error loading products!</AlertTitle>
                    <AlertDescription>
                        There was an error loading your selling items. Please try again.
                    </AlertDescription>
                </Alert>
            ) : products.length === 0 ? (
                <Card.Root>
                    <Card.Body textAlign="center" py={12}>
                        <VStack gap={4}>
                            <Box fontSize="4xl">📦</Box>
                            <Heading size="md" color="gray.600">No items found</Heading>
                            <Text color="gray.500">
                                {searchTerm || statusFilter !== 'all' || sellTypeFilter !== 'all'
                                    ? 'No items match your current filters.'
                                    : "You haven't listed any items yet."
                                }
                            </Text>
                            <Button
                                colorScheme="blue"
                                leftIcon={<FaPlus />}
                                onClick={() => router.push('/selling/create')}
                            >
                                List Your First Item
                            </Button>
                        </VStack>
                    </Card.Body>
                </Card.Root>
            ) : (
                <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)", lg: "repeat(3, 1fr)" }} gap={6}>
                    {products.map((product) => {
                        const mainImage = product.images.find(img => img.isMain) || product.images[0]
                        const isAuction = product.sellType === 'auction'
                        const timeLeft = product.auctionEndDate
                            ? formatDistanceToNow(new Date(product.auctionEndDate), { addSuffix: true })
                            : null

                        return (
                            <Card.Root key={product.id} overflow="hidden">
                                <Box position="relative">
                                    <Image
                                        src={mainImage?.imageUrl}
                                        alt={product.itemName}
                                        height="200px"
                                        width="100%"
                                        objectFit="cover"
                                    />
                                    <Badge
                                        position="absolute"
                                        top={2}
                                        left={2}
                                        colorScheme={getStatusColor(product.status)}
                                    >
                                        {getStatusText(product.status)}
                                    </Badge>
                                    <Badge
                                        position="absolute"
                                        top={2}
                                        right={2}
                                        colorScheme={isAuction ? 'blue' : 'green'}
                                    >
                                        {isAuction ? <FaGavel /> : <FaShoppingCart />}
                                        {isAuction ? 'Auction' : 'Buy Now'}
                                    </Badge>
                                </Box>

                                <Card.Body>
                                    <VStack align="stretch" gap={3}>
                                        <Box>
                                            <Text fontWeight="bold" fontSize="md" noOfLines={2} mb={1}>
                                                {product.itemName}
                                            </Text>
                                            <Text fontSize="sm" color="gray.600">
                                                {product.category?.name}
                                            </Text>
                                        </Box>

                                        <HStack justify="space-between">
                                            <VStack align="start" gap={0}>
                                                <Text fontSize="xs" color="gray.500">
                                                    {isAuction ? 'Current Bid' : 'Price'}
                                                </Text>
                                                <Text fontWeight="bold" color="blue.600">
                                                    {formatUSD(Number(product.currentBid || product.priceUSD))}
                                                </Text>
                                            </VStack>
                                            {isAuction && (
                                                <VStack align="end" gap={0}>
                                                    <Text fontSize="xs" color="gray.500">
                                                        Bids
                                                    </Text>
                                                    <Text fontWeight="bold">
                                                        {product.bidCount || 0}
                                                    </Text>
                                                </VStack>
                                            )}
                                        </HStack>

                                        {isAuction && timeLeft && product.status === 'active' && (
                                            <HStack>
                                                <FaClock size={12} color="gray" />
                                                <Text fontSize="xs" color="gray.600">
                                                    {timeLeft}
                                                </Text>
                                            </HStack>
                                        )}

                                        <HStack justify="space-between">
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                leftIcon={<FaEye />}
                                                onClick={() => handleViewProduct(product.slug || product.id)}
                                            >
                                                View
                                            </Button>

                                            <Menu>
                                                <MenuButton
                                                    as={IconButton}
                                                    icon={<FaEllipsisV />}
                                                    size="sm"
                                                    variant="ghost"
                                                />
                                                <MenuList>
                                                    <MenuItem
                                                        icon={<FaEdit />}
                                                        onClick={() => handleEditProduct(product.id)}
                                                    >
                                                        Edit
                                                    </MenuItem>
                                                    <MenuItem
                                                        icon={<FaTrash />}
                                                        color="red.600"
                                                        onClick={() => handleDeleteProduct(product.id, product.itemName)}
                                                    >
                                                        Delete
                                                    </MenuItem>
                                                </MenuList>
                                            </Menu>
                                        </HStack>
                                    </VStack>
                                </Card.Body>
                            </Card.Root>
                        )
                    })}
                </Grid>
            )}
        </Box>
    )
}

export default SellingPage
