'use client'

import { Box, Heading, VStack } from '@chakra-ui/react';
import ImageUpload from '@/components/ui/ImageUpload';

const TestUploadPage = () => {
  const handleImagesUploaded = (urls: string[]) => {
    console.log('Images uploaded successfully:', urls);
    alert(`Successfully uploaded ${urls.length} images!`);
  };

  return (
    <Box maxW="800px" mx="auto" p={8}>
      <VStack gap={6} align="stretch">
        <Heading size="lg" textAlign="center">
          Test Image Upload
        </Heading>
        
        <ImageUpload
          onImagesUploaded={handleImagesUploaded}
          maxFiles={5}
        />
      </VStack>
    </Box>
  );
};

export default TestUploadPage;
