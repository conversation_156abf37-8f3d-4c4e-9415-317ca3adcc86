'use client'
import React, { useState } from 'react'
import {
    Box,
    Button,
    Card,
    Heading,
    Text,
    VStack,
    HStack,
    Stack,
    Input,
    Textarea,
    Select,
    Image,
    Badge,
    Separator,
    Grid,
    GridItem,
    FormControl,
    FormLabel,
    FormErrorMessage,
    Alert,
    AlertIcon,
    AlertTitle,
    AlertDescription,
} from '@chakra-ui/react'
import { useForm } from 'react-hook-form'
import { useCartQuery } from '@/services/useCartQuery'
import { useCreateOrderMutation } from '@/services/useCheckoutQuery'
import { formatUSD } from '@/utils/helpers/helper'
import { toaster } from '@/components/ui/toaster'
import { useRouter } from 'next/navigation'

interface CheckoutFormData {
    // Shipping Address
    firstName: string
    lastName: string
    email: string
    phone: string
    address: string
    city: string
    state: string
    zipCode: string
    country: string
    
    // Payment
    paymentMethod: 'credit_card' | 'paypal' | 'bank_transfer'
    
    // Notes
    notes?: string
}

const CheckoutPage = () => {
    const router = useRouter()
    const { data: cart, isLoading: cartLoading } = useCartQuery()
    const createOrderMutation = useCreateOrderMutation()
    
    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
        watch
    } = useForm<CheckoutFormData>({
        defaultValues: {
            country: 'US',
            paymentMethod: 'credit_card'
        }
    })

    const paymentMethod = watch('paymentMethod')

    const onSubmit = async (data: CheckoutFormData) => {
        if (!cart || cart.items.length === 0) {
            toaster.create({
                title: "Error",
                description: "Your cart is empty",
                type: "error",
            })
            return
        }

        try {
            const orderData = {
                items: cart.items.map(item => ({
                    productId: item.productId,
                    quantity: item.quantity
                })),
                shippingAddress: {
                    firstName: data.firstName,
                    lastName: data.lastName,
                    email: data.email,
                    phone: data.phone,
                    address: data.address,
                    city: data.city,
                    state: data.state,
                    zipCode: data.zipCode,
                    country: data.country,
                },
                paymentMethod: {
                    type: data.paymentMethod,
                    // In a real app, you'd collect payment details here
                },
                notes: data.notes
            }

            const order = await createOrderMutation.mutateAsync(orderData)
            
            toaster.create({
                title: "Order Created",
                description: `Order #${order.orderNumber} has been created successfully!`,
                type: "success",
            })
            
            // Redirect to order confirmation
            router.push(`/orders/${order.id}`)
        } catch (error) {
            console.error('Checkout failed:', error)
        }
    }

    if (cartLoading) {
        return (
            <Box maxW="6xl" mx="auto" p={6}>
                <Text>Loading checkout...</Text>
            </Box>
        )
    }

    if (!cart || cart.items.length === 0) {
        return (
            <Box maxW="6xl" mx="auto" p={6} textAlign="center">
                <Alert status="warning">
                    <AlertIcon />
                    <AlertTitle>Cart is empty!</AlertTitle>
                    <AlertDescription>
                        Add some items to your cart before checking out.
                    </AlertDescription>
                </Alert>
                <Button mt={4} onClick={() => router.push('/')}>
                    Continue Shopping
                </Button>
            </Box>
        )
    }

    return (
        <Box maxW="6xl" mx="auto" p={6}>
            <Heading mb={6}>Checkout</Heading>
            
            <form onSubmit={handleSubmit(onSubmit)}>
                <Grid templateColumns={{ base: "1fr", lg: "2fr 1fr" }} gap={8}>
                    {/* Left Column - Forms */}
                    <GridItem>
                        <VStack align="stretch" gap={6}>
                            {/* Shipping Information */}
                            <Card.Root>
                                <Card.Header>
                                    <Heading size="md">Shipping Information</Heading>
                                </Card.Header>
                                <Card.Body>
                                    <Grid templateColumns={{ base: "1fr", md: "1fr 1fr" }} gap={4}>
                                        <FormControl isInvalid={!!errors.firstName}>
                                            <FormLabel>First Name</FormLabel>
                                            <Input
                                                {...register('firstName', { required: 'First name is required' })}
                                                placeholder="John"
                                            />
                                            <FormErrorMessage>
                                                {errors.firstName?.message}
                                            </FormErrorMessage>
                                        </FormControl>

                                        <FormControl isInvalid={!!errors.lastName}>
                                            <FormLabel>Last Name</FormLabel>
                                            <Input
                                                {...register('lastName', { required: 'Last name is required' })}
                                                placeholder="Doe"
                                            />
                                            <FormErrorMessage>
                                                {errors.lastName?.message}
                                            </FormErrorMessage>
                                        </FormControl>

                                        <FormControl isInvalid={!!errors.email}>
                                            <FormLabel>Email</FormLabel>
                                            <Input
                                                type="email"
                                                {...register('email', { 
                                                    required: 'Email is required',
                                                    pattern: {
                                                        value: /^\S+@\S+$/i,
                                                        message: 'Invalid email address'
                                                    }
                                                })}
                                                placeholder="<EMAIL>"
                                            />
                                            <FormErrorMessage>
                                                {errors.email?.message}
                                            </FormErrorMessage>
                                        </FormControl>

                                        <FormControl isInvalid={!!errors.phone}>
                                            <FormLabel>Phone</FormLabel>
                                            <Input
                                                {...register('phone', { required: 'Phone is required' })}
                                                placeholder="+****************"
                                            />
                                            <FormErrorMessage>
                                                {errors.phone?.message}
                                            </FormErrorMessage>
                                        </FormControl>
                                    </Grid>

                                    <FormControl isInvalid={!!errors.address} mt={4}>
                                        <FormLabel>Address</FormLabel>
                                        <Input
                                            {...register('address', { required: 'Address is required' })}
                                            placeholder="123 Main Street"
                                        />
                                        <FormErrorMessage>
                                            {errors.address?.message}
                                        </FormErrorMessage>
                                    </FormControl>

                                    <Grid templateColumns={{ base: "1fr", md: "1fr 1fr 1fr" }} gap={4} mt={4}>
                                        <FormControl isInvalid={!!errors.city}>
                                            <FormLabel>City</FormLabel>
                                            <Input
                                                {...register('city', { required: 'City is required' })}
                                                placeholder="New York"
                                            />
                                            <FormErrorMessage>
                                                {errors.city?.message}
                                            </FormErrorMessage>
                                        </FormControl>

                                        <FormControl isInvalid={!!errors.state}>
                                            <FormLabel>State</FormLabel>
                                            <Input
                                                {...register('state', { required: 'State is required' })}
                                                placeholder="NY"
                                            />
                                            <FormErrorMessage>
                                                {errors.state?.message}
                                            </FormErrorMessage>
                                        </FormControl>

                                        <FormControl isInvalid={!!errors.zipCode}>
                                            <FormLabel>ZIP Code</FormLabel>
                                            <Input
                                                {...register('zipCode', { required: 'ZIP code is required' })}
                                                placeholder="10001"
                                            />
                                            <FormErrorMessage>
                                                {errors.zipCode?.message}
                                            </FormErrorMessage>
                                        </FormControl>
                                    </Grid>

                                    <FormControl isInvalid={!!errors.country} mt={4}>
                                        <FormLabel>Country</FormLabel>
                                        <Select {...register('country', { required: 'Country is required' })}>
                                            <option value="US">United States</option>
                                            <option value="CA">Canada</option>
                                            <option value="UK">United Kingdom</option>
                                            <option value="AU">Australia</option>
                                        </Select>
                                        <FormErrorMessage>
                                            {errors.country?.message}
                                        </FormErrorMessage>
                                    </FormControl>
                                </Card.Body>
                            </Card.Root>

                            {/* Payment Method */}
                            <Card.Root>
                                <Card.Header>
                                    <Heading size="md">Payment Method</Heading>
                                </Card.Header>
                                <Card.Body>
                                    <FormControl isInvalid={!!errors.paymentMethod}>
                                        <FormLabel>Select Payment Method</FormLabel>
                                        <Select {...register('paymentMethod', { required: 'Payment method is required' })}>
                                            <option value="credit_card">Credit Card</option>
                                            <option value="paypal">PayPal</option>
                                            <option value="bank_transfer">Bank Transfer</option>
                                        </Select>
                                        <FormErrorMessage>
                                            {errors.paymentMethod?.message}
                                        </FormErrorMessage>
                                    </FormControl>

                                    {paymentMethod === 'credit_card' && (
                                        <Alert status="info" mt={4}>
                                            <AlertIcon />
                                            <AlertDescription>
                                                Credit card processing will be handled securely on the next step.
                                            </AlertDescription>
                                        </Alert>
                                    )}

                                    {paymentMethod === 'bank_transfer' && (
                                        <Alert status="info" mt={4}>
                                            <AlertIcon />
                                            <AlertDescription>
                                                Bank transfer details will be provided after order confirmation.
                                            </AlertDescription>
                                        </Alert>
                                    )}
                                </Card.Body>
                            </Card.Root>

                            {/* Order Notes */}
                            <Card.Root>
                                <Card.Header>
                                    <Heading size="md">Order Notes (Optional)</Heading>
                                </Card.Header>
                                <Card.Body>
                                    <FormControl>
                                        <FormLabel>Special Instructions</FormLabel>
                                        <Textarea
                                            {...register('notes')}
                                            placeholder="Any special delivery instructions or notes..."
                                            rows={3}
                                        />
                                    </FormControl>
                                </Card.Body>
                            </Card.Root>
                        </VStack>
                    </GridItem>

                    {/* Right Column - Order Summary */}
                    <GridItem>
                        <Card.Root position="sticky" top={6}>
                            <Card.Header>
                                <Heading size="md">Order Summary</Heading>
                            </Card.Header>
                            <Card.Body>
                                <VStack align="stretch" gap={4}>
                                    {/* Cart Items */}
                                    {cart.items.map((item) => {
                                        const mainImage = item.product.images.find(img => img.isMain) || item.product.images[0]
                                        
                                        return (
                                            <HStack key={item.id} gap={3}>
                                                <Image
                                                    src={mainImage?.imageUrl}
                                                    alt={item.product.itemName}
                                                    boxSize="60px"
                                                    objectFit="cover"
                                                    borderRadius="md"
                                                />
                                                <VStack align="start" flex={1} gap={1}>
                                                    <Text fontSize="sm" fontWeight="medium" noOfLines={2}>
                                                        {item.product.itemName}
                                                    </Text>
                                                    <HStack>
                                                        <Text fontSize="xs" color="gray.600">
                                                            Qty: {item.quantity}
                                                        </Text>
                                                        <Badge colorScheme={item.product.sellType === 'auction' ? 'blue' : 'green'}>
                                                            {item.product.sellType === 'auction' ? 'Auction' : 'Buy Now'}
                                                        </Badge>
                                                    </HStack>
                                                </VStack>
                                                <Text fontWeight="bold">
                                                    {formatUSD(item.price * item.quantity)}
                                                </Text>
                                            </HStack>
                                        )
                                    })}

                                    <Separator />

                                    {/* Order Totals */}
                                    <VStack align="stretch" gap={2}>
                                        <HStack justify="space-between">
                                            <Text>Subtotal:</Text>
                                            <Text>{formatUSD(cart.totalPrice)}</Text>
                                        </HStack>
                                        <HStack justify="space-between">
                                            <Text>Shipping:</Text>
                                            <Text>Free</Text>
                                        </HStack>
                                        <HStack justify="space-between">
                                            <Text>Tax:</Text>
                                            <Text>{formatUSD(cart.totalPrice * 0.08)}</Text>
                                        </HStack>
                                        <Separator />
                                        <HStack justify="space-between">
                                            <Text fontWeight="bold" fontSize="lg">Total:</Text>
                                            <Text fontWeight="bold" fontSize="lg" color="blue.600">
                                                {formatUSD(cart.totalPrice * 1.08)}
                                            </Text>
                                        </HStack>
                                    </VStack>

                                    <Button
                                        type="submit"
                                        colorScheme="blue"
                                        size="lg"
                                        w="full"
                                        loading={isSubmitting || createOrderMutation.isPending}
                                        disabled={isSubmitting || createOrderMutation.isPending}
                                    >
                                        Place Order
                                    </Button>

                                    <Text fontSize="xs" color="gray.500" textAlign="center">
                                        By placing this order, you agree to our Terms of Service and Privacy Policy.
                                    </Text>
                                </VStack>
                            </Card.Body>
                        </Card.Root>
                    </GridItem>
                </Grid>
            </form>
        </Box>
    )
}

export default CheckoutPage
